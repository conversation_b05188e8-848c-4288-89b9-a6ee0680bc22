//
//  ContentView.swift
//  reftest Watch App
//
//  Created by user280793 on 8/7/25.
//

import SwiftUI

struct ContentView: View {
    @State private var showingParameters = false
    @State private var showingWorkout = false
    @State private var showingInfo = false
    
    var body: some View {
        ZStack {
            // Background color
            Color.black.ignoresSafeArea()
            
            // Main container that respects the safe areas by default
            VStack {
                // The container for the title text
                HStack {
                    Text("RefTest")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                    
                    // Pushes the text to the left
                    Spacer()
                }
                // FIX: Increased the negative padding significantly to pull the title up higher.
                .padding(.top, -35.29)
                .padding(.horizontal)

                // Spacer to push the buttons down into the center
                Spacer()
                
                // The block of buttons remains the same
                // FIX: Increased the spacing between the buttons from 8 to 12
                VStack(spacing: 12) {
                    Button(action: {
                        showingParameters = true
                    }) {
                        Text("PARAMETERS")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 32)
                            .background(Color(red: 0.3, green: 0.3, blue: 0.3)) // Dark grey
                            .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    Button(action: {
                        showingWorkout = true
                    }) {
                        Text("START TEST")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 32)
                            .background(Color(red: 0.3, green: 0.3, blue: 0.3)) // Dark grey
                            .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    Button(action: {
                        showingInfo = true
                    }) {
                        Text("INFO")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 32)
                            .background(Color(red: 0.3, green: 0.3, blue: 0.3)) // Dark grey
                            .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .padding(.horizontal, 16)
                
                // Spacer to ensure the buttons are centered vertically
                Spacer()
            }
        }
        .sheet(isPresented: $showingParameters) {
            ParametersView()
        }
        .sheet(isPresented: $showingWorkout) {
            WorkoutView()
        }
        .sheet(isPresented: $showingInfo) {
            InfoView()
        }
    }
}

#Preview {
    ContentView()
}
