<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Assets.car</key>
		<data>
		5G77NAFsaQZiNo+f2piQLPUbjJU=
		</data>
		<key>Info.plist</key>
		<data>
		TdgFX+c6EJr4s37+Oi2+q7NxlvQ=
		</data>
		<key><EMAIL></key>
		<data>
		OuSBL5KZUfa+JUWKjBihMrVIUFs=
		</data>
		<key>MessagesApplicationStub76x76@2x~ipad.png</key>
		<data>
		cpa4cDhCJtj1Bd/9pOwTkdW6kts=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>Watch/reftest Watch App.app/Assets.car</key>
		<data>
		CBXQTwDsNT4oIz+3MfXKWB4oKTU=
		</data>
		<key>Watch/reftest Watch App.app/Info.plist</key>
		<data>
		p4AoMX6+X/zFFQBRXGDlwDDJKSg=
		</data>
		<key>Watch/reftest Watch App.app/PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>Watch/reftest Watch App.app/_CodeSignature/CodeResources</key>
		<data>
		B1HZf0yNgaSm+N6E106FPg1+rk4=
		</data>
		<key>Watch/reftest Watch App.app/embedded.mobileprovision</key>
		<data>
		EMMHydbadoDouHOMwst7+qmo6FE=
		</data>
		<key>Watch/reftest Watch App.app/reftest Watch App</key>
		<data>
		3HNQvNEohMPU355y3Uwx+nU31Lg=
		</data>
		<key>embedded.mobileprovision</key>
		<data>
		Hsap0hPkOJkui+DyYAdFuIQwkQw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			5G77NAFsaQZiNo+f2piQLPUbjJU=
			</data>
			<key>hash2</key>
			<data>
			t9mgxvlEkKo8SGlW7J7nn/zRYI+JXN3L0KMKXqEi49g=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			OuSBL5KZUfa+JUWKjBihMrVIUFs=
			</data>
			<key>hash2</key>
			<data>
			lbEVUbakzRRWxR/wmC1hIoqAJBH8NKeg+00Cx2rdtUQ=
			</data>
		</dict>
		<key>MessagesApplicationStub76x76@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			cpa4cDhCJtj1Bd/9pOwTkdW6kts=
			</data>
			<key>hash2</key>
			<data>
			0pg4DSI8hVctDBBL/RZwFobgHpXbm25lanZGPbxV0F0=
			</data>
		</dict>
		<key>Watch/reftest Watch App.app/Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			CBXQTwDsNT4oIz+3MfXKWB4oKTU=
			</data>
			<key>hash2</key>
			<data>
			Rbxz4IK+ZpeZNTdnUI+icKvy7rmm+4FWuVNxdHhvuI4=
			</data>
		</dict>
		<key>Watch/reftest Watch App.app/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			p4AoMX6+X/zFFQBRXGDlwDDJKSg=
			</data>
			<key>hash2</key>
			<data>
			GNfOkr5L2SOv5YZ7uBpC9gxFyWFDKaRXPsMfd1ktK8o=
			</data>
		</dict>
		<key>Watch/reftest Watch App.app/PkgInfo</key>
		<dict>
			<key>hash</key>
			<data>
			n57qDP4tZfLD1rCS43W0B4LQjzE=
			</data>
			<key>hash2</key>
			<data>
			glAhkclISwTWhTdPmHmgBmBpxJuKyuegSwHTjQfo7KA=
			</data>
		</dict>
		<key>Watch/reftest Watch App.app/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			B1HZf0yNgaSm+N6E106FPg1+rk4=
			</data>
			<key>hash2</key>
			<data>
			1JkvUdYdO9JwpTrLkUvbXte+7WivJyLChT7CTBT6oYA=
			</data>
		</dict>
		<key>Watch/reftest Watch App.app/embedded.mobileprovision</key>
		<dict>
			<key>hash</key>
			<data>
			EMMHydbadoDouHOMwst7+qmo6FE=
			</data>
			<key>hash2</key>
			<data>
			jLbFMb0I7b5WBdfTjs1O+0h/waMfTBJwgaNajAxCPqY=
			</data>
		</dict>
		<key>Watch/reftest Watch App.app/reftest Watch App</key>
		<dict>
			<key>hash</key>
			<data>
			3HNQvNEohMPU355y3Uwx+nU31Lg=
			</data>
			<key>hash2</key>
			<data>
			8my+AVdpRXLo4ath4VR+hJo5wwrXyh7P/2MZtBCe/CE=
			</data>
		</dict>
		<key>embedded.mobileprovision</key>
		<dict>
			<key>hash</key>
			<data>
			Hsap0hPkOJkui+DyYAdFuIQwkQw=
			</data>
			<key>hash2</key>
			<data>
			HAOLfWC+Y6roBm3/e3A2k27z7RPiPcoHJ11pJCHm7Q4=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
