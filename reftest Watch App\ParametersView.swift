import SwiftUI

struct ParametersView: View {
    @Environment(\.dismiss) private var dismiss
    @AppStorage("jogSeconds") private var jogSeconds = 15
    @AppStorage("walkSeconds") private var walkSeconds = 25
    @AppStorage("intervals") private var intervals = 15

    var body: some View {
        ZStack {
            Color.black
                .ignoresSafeArea()

            GeometryReader { geo in
                // base scale computed from the smallest side (width or height)
                let minSide = min(geo.size.width, geo.size.height)
                let base: CGFloat = 170.0
                let rawScale = minSide / base
                let scale = max(0.78, min(1.35, rawScale)) // controls global scaling, tweakable

                // Layout constants
                let topPadding: CGFloat = 8 * scale        // small top offset to push rows down slightly
                let interRowSpacing: CGFloat = 8 * scale  // spacing between rows
                let saveButtonHeight: CGFloat = max(34 * scale, 32) // visible tappable SAVE height
                let saveBottomPadding: CGFloat = 12 * scale // extra bottom padding so <PERSON><PERSON> sits lower
                let verticalMargins: CGFloat = topPadding + saveButtonHeight + saveBottomPadding + (16 * scale) // additional buffer

                // Compute the available height for the three rows
                let totalHeight = geo.size.height
                let availableForRows = max(0, totalHeight - verticalMargins)

                // Compute each row height, distribute available space. Enforce min & max
                let candidateRowHeight = (availableForRows - (2 * interRowSpacing)) / 3
                let minRowHeight: CGFloat = 36 * scale
                let maxRowHeight: CGFloat = 64 * scale
                // final height per row (clamped)
                let rowHeight = max(minRowHeight, min(maxRowHeight, candidateRowHeight))

                VStack(spacing: 0) {
                    // small top push so rows are slightly lower on screen
                    Spacer()
                        .frame(height: topPadding)

                    // Rows container
                    VStack(spacing: interRowSpacing) {
                        ParameterRow(
                            label: "Jog",
                            value: $jogSeconds,
                            suffix: "s",
                            range: 1...60,
                            rowHeight: rowHeight,
                            scale: scale
                        )

                        ParameterRow(
                            label: "Walk",
                            value: $walkSeconds,
                            suffix: "s",
                            range: 1...60,
                            rowHeight: rowHeight,
                            scale: scale
                        )

                        ParameterRow(
                            label: "Intervals",
                            value: $intervals,
                            suffix: "",
                            range: 1...50,
                            rowHeight: rowHeight,
                            scale: scale
                        )
                    }
                    .padding(.horizontal, 14 * scale)
                    // ensure rows block consumes exactly the expected space
                    .frame(height: (rowHeight * 3) + (interRowSpacing * 2), alignment: .top)

                    Spacer()

                    // SAVE button placed lower (extra bottom padding) so rows sit higher
                    Button(action: {
                        // Values auto-saved via @AppStorage
                        dismiss()
                    }) {
                        Text("SAVE")
                            .font(.system(size: max(14 * scale, 12), weight: .medium))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: saveButtonHeight)
                            .background(Color.green)
                            .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .padding(.horizontal, 16 * scale)
                    .padding(.bottom, saveBottomPadding)
                    .offset(y: 15)
                }
                .frame(width: geo.size.width, height: geo.size.height)
            } // GeometryReader
        } // ZStack
    }
}

struct ParameterRow: View {
    let label: String
    @Binding var value: Int
    let suffix: String
    let range: ClosedRange<Int>
    let rowHeight: CGFloat
    let scale: CGFloat

    var body: some View {
        HStack {
            // Label
            Text(label)
                .font(.system(size: labelFontSize(), weight: .medium))
                .foregroundColor(.white)
                .lineLimit(1)
                .frame(minWidth: 40 * scale, alignment: .leading)
                .layoutPriority(1)

            Spacer()

            HStack(spacing: 12 * scale) {
                Button(action: {
                    if value > range.lowerBound {
                        value -= 1
                    }
                }) {
                    Image(systemName: "minus.circle.fill")
                        .font(.system(size: iconSize()))
                        .foregroundColor(.white)
                }
                .buttonStyle(PlainButtonStyle())

                Text("\(value)\(suffix)")
                    .font(.system(size: valueFontSize(), weight: .medium))
                    .foregroundColor(.white)
                    .lineLimit(1)
                    .frame(minWidth: valueMinWidth(), alignment: .center)
                    .layoutPriority(2)

                Button(action: {
                    if value < range.upperBound {
                        value += 1
                    }
                }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: iconSize()))
                        .foregroundColor(.white)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.horizontal, 10 * scale)
        .padding(.vertical, max(6 * scale, rowHeight * 0.1))
        .background(Color(red: 0.3, green: 0.3, blue: 0.3))
        .cornerRadius(8)
        .frame(height: rowHeight) // fixed per-computed height so nothing moves offscreen
    }

    // MARK: - helpers for sizes (kept readable)
    private func labelFontSize() -> CGFloat {
        // roughly 30-38% of rowHeight, bounded
        let size = rowHeight * 0.32
        return max(11, min(16, size))
    }

    private func valueFontSize() -> CGFloat {
        let size = rowHeight * 0.36
        return max(12, min(18, size))
    }

    private func iconSize() -> CGFloat {
        let size = rowHeight * 0.42
        return max(16, min(22, size))
    }

    private func valueMinWidth() -> CGFloat {
        // ensure value text has a stable width for layout
        return max(32 * scale, rowHeight * 0.45)
    }
}

// MARK: - Preview
struct ParametersView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            ParametersView()
                .previewDevice(PreviewDevice(rawValue: "Apple Watch Series 9 - 45mm"))
                .preferredColorScheme(.dark)

            ParametersView()
                .previewDevice(PreviewDevice(rawValue: "Apple Watch SE - 40mm"))
                .preferredColorScheme(.dark)

            ParametersView()
                .previewDevice(PreviewDevice(rawValue: "Apple Watch Series 3 - 38mm"))
                .preferredColorScheme(.dark)

            ParametersView()
                .previewDevice(PreviewDevice(rawValue: "Apple Watch Ultra - 49mm"))
                .preferredColorScheme(.dark)
        }
        .environment(\.isEnabled, true)
    }
}
