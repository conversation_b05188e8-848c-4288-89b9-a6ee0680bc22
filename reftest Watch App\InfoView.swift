//
//  InfoView.swift
//  reftest Watch App
//
//  Created by user280793 on 8/7/25.
//

import SwiftUI

struct InfoView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        ZStack {
            // Background - black to match the watch face
            Color.black
                .ignoresSafeArea()

            VStack(spacing: 16) {
                Spacer()

                VStack(spacing: 12) {
                    Text("App Version 1.0")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white)
                        .foregroundStyle(Color.white)
                        .opacity(1)

                    Text("Developed by RefTest Team")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(.white)
                        .foregroundStyle(Color.white)
                        .multilineTextAlignment(.center)
                        .opacity(1)

                    // force white and full opacity for the email
                    Text("<EMAIL>")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(.white)        // explicit color
                        .foregroundStyle(Color.white)   // also set foregroundStyle
                        .opacity(1)                     // ensure not dimmed
                        .multilineTextAlignment(.center)
                        .layoutPriority(1)              // prefer this not to be truncated
                }
                .padding(.horizontal, 20)

                Spacer()

                // Back button
                Button(action: {
                    dismiss()
                }) {
                    Text("Back")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 32)
                        .background(Color.gray)
                        .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.horizontal, 16)
                .padding(.bottom, 8)
            }
        }
    }
}


#Preview {
    InfoView()
}
