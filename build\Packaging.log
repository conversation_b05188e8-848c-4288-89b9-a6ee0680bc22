2025-08-15 11:30:25 +0000 [MT] Initial pipeline context: <IDEDistributionProcessingPipelineContext: 0x14b7a10e0; archive(resolved)="<IDEArchive: 0x600002b97f70>", distributionTask(resolved)="2", distributionDestination(resolved)="1", distributionMethod(resolved)="<IDEDistributionMethodiOSAppStoreDistribution: 0x600000271120>", team(resolved)="<IDEProvisioningBasicTeam: 0x60000017e560; teamID='3QD4HY4WVJ', teamName='(null)'>">
	Chain (15, self inclusive):
	<IDEDistributionProcessingPipelineContext: 0x14b7a10e0; archive = "<IDEArchive: 0x600002b97f70>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x600000271120>", team="<IDEProvisioningBasicTeam: 0x60000017e560; teamID='3QD4HY4WVJ', teamName='(null)'>">
	<IDEDistributionProcessingPipelineContext: 0x14c94cb40; archive = "<IDEArchive: 0x600002b97f70>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x600000271120>", team="<IDEProvisioningBasicTeam: 0x60000017e560; teamID='3QD4HY4WVJ', teamName='(null)'>">
	<IDEDistributionContext: 0x14c94c930; archive = "<IDEArchive: 0x600002b97f70>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x600000271120>", team="<IDEProvisioningBasicTeam: 0x60000017e560; teamID='3QD4HY4WVJ', teamName='(null)'>">
	<IDEDistributionContext: 0x14b67f5d0; archive = "<IDEArchive: 0x600002b97f70>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x600000271120>", team="<IDEProvisioningBasicTeam: 0x60000017e560; teamID='3QD4HY4WVJ', teamName='(null)'>">
	<IDEDistributionContext: 0x14b76bd10; archive = "<IDEArchive: 0x600002b97f70>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x600000271120>", team="<IDEProvisioningBasicTeam: 0x60000017e560; teamID='3QD4HY4WVJ', teamName='(null)'>">
	<IDEDistributionContext: 0x14c8e6750; archive = "<IDEArchive: 0x600002b97f70>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x600000271120>", team="<IDEProvisioningBasicTeam: 0x60000017e560; teamID='3QD4HY4WVJ', teamName='(null)'>">
	<IDEDistributionContext: 0x14b67a8f0; archive = "<IDEArchive: 0x600002b97f70>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x600000271120>", team="<IDEProvisioningBasicTeam: 0x60000017e560; teamID='3QD4HY4WVJ', teamName='(null)'>">
	<IDEDistributionContext: 0x14c8e3950; archive = "<IDEArchive: 0x600002b97f70>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x600000271120>", team="<IDEProvisioningBasicTeam: 0x60000017e560; teamID='3QD4HY4WVJ', teamName='(null)'>">
	<IDEDistributionContext: 0x14b67bdd0; archive = "<IDEArchive: 0x600002b97f70>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x600000271120>", team="<IDEProvisioningBasicTeam: 0x60000017e560; teamID='3QD4HY4WVJ', teamName='(null)'>">
	<IDEDistributionContext: 0x14c925470; archive = "<IDEArchive: 0x600002b97f70>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x600000271120>", team="<IDEProvisioningBasicTeam: 0x60000017e560; teamID='3QD4HY4WVJ', teamName='(null)'>">
	<IDEDistributionContext: 0x14b679690; archive = "<IDEArchive: 0x600002b97f70>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x600000271120>", team="<IDEProvisioningBasicTeam: 0x60000017e560; teamID='3QD4HY4WVJ', teamName='(null)'>">
	<IDEDistributionContext: 0x14b7716f0; archive = "<IDEArchive: 0x600002b97f70>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x600000271120>", team="<IDEProvisioningBasicTeam: 0x60000017e560; teamID='3QD4HY4WVJ', teamName='(null)'>">
	<IDEDistributionContext: 0x14b67b5e0; archive = "<IDEArchive: 0x600002b97f70>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x600000271120>", team="(null)">
	<IDEDistributionContext: 0x14c8d2390; archive = "<IDEArchive: 0x600002b97f70>", distributionMethod="<IDEDistributionMethodiOSAppStoreDistribution: 0x600000271120>", team="(null)">
	<IDEDistributionContext: 0x14b642300; archive = "<IDEArchive: 0x600002b97f70>", distributionMethod="(null)", team="(null)">
</IDEDistributionProcessingPipelineContext: 0x14b7a10e0>
2025-08-15 11:30:25 +0000 [MT] Processing step: IDEDistributionCreateDestRootStep
2025-08-15 11:30:25 +0000 [MT] Processing step: IDEDistributionCopyItemStep
2025-08-15 11:30:25 +0000 [MT] Running /usr/bin/ditto '-V' '/Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app' '/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Root/Payload/reftest.app'
2025-08-15 11:30:25 +0000  >>> Copying /Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app 
2025-08-15 11:30:25 +0000  copying file ./_CodeSignature/CodeResources ... 
2025-08-15 11:30:25 +0000  5203 bytes for ./_CodeSignature/CodeResources
2025-08-15 11:30:25 +0000  copying file ./reftest ... 
2025-08-15 11:30:25 +0000  69264 bytes for ./reftest
2025-08-15 11:30:25 +0000  copying file ./Assets.car ... 
2025-08-15 11:30:25 +0000  25823 bytes for ./Assets.car
2025-08-15 11:30:25 +0000  copying file ./Watch/reftest Watch App.app/_CodeSignature/CodeResources ... 
2025-08-15 11:30:25 +0000  2461 bytes for ./Watch/reftest Watch App.app/_CodeSignature/CodeResources
2025-08-15 11:30:25 +0000  copying file ./Watch/reftest Watch App.app/Assets.car ... 
2025-08-15 11:30:25 +0000  74639 bytes for ./Watch/reftest Watch App.app/Assets.car
2025-08-15 11:30:25 +0000  copying file ./Watch/reftest Watch App.app/embedded.mobileprovision ... 
2025-08-15 11:30:25 +0000  12642 bytes for ./Watch/reftest Watch App.app/embedded.mobileprovision
2025-08-15 11:30:25 +0000  copying file ./Watch/reftest Watch App.app/reftest Watch App ... 
2025-08-15 11:30:25 +0000  537632 bytes for ./Watch/reftest Watch App.app/reftest Watch App
2025-08-15 11:30:25 +0000  copying file ./Watch/reftest Watch App.app/Info.plist ... 
1180 bytes for ./Watch/reftest Watch App.app/Info.plist
copying file ./Watch/reftest Watch App.app/PkgInfo ... 
2025-08-15 11:30:25 +0000  8 bytes for ./Watch/reftest Watch App.app/PkgInfo
2025-08-15 11:30:25 +0000  copying file ./MessagesApplicationStub76x76@2x~ipad.png ... 
2025-08-15 11:30:25 +0000  591 bytes for ./MessagesApplicationStub76x76@2x~ipad.png
2025-08-15 11:30:25 +0000  copying file ./embedded.mobileprovision ... 
2025-08-15 11:30:25 +0000  12620 bytes for ./embedded.mobileprovision
2025-08-15 11:30:25 +0000  copying file ./<EMAIL> ... 
2025-08-15 11:30:25 +0000  502 bytes for ./<EMAIL>
2025-08-15 11:30:25 +0000  copying file ./Info.plist ... 
2025-08-15 11:30:25 +0000  1112 bytes for ./Info.plist
2025-08-15 11:30:25 +0000  copying file ./PkgInfo ... 
2025-08-15 11:30:25 +0000  8 bytes for ./PkgInfo
2025-08-15 11:30:25 +0000 [MT] /usr/bin/ditto exited with 0
2025-08-15 11:30:25 +0000 [MT] Processing step: IDEDistributionEmbedProfileStep
2025-08-15 11:30:25 +0000 [MT] Processing step: IDEDistributionInfoPlistStep
2025-08-15 11:30:25 +0000 [MT] Skipping setting DTXcodeBuildDistribution because toolsBuildVersionName was nil.
2025-08-15 11:30:25 +0000 [MT] Skipping setting DTXcodeBuildDistribution because toolsBuildVersionName was nil.
2025-08-15 11:30:25 +0000 [MT] Processing step: IDEDistributionItemRemovalStep
2025-08-15 11:30:25 +0000 [MT] Processing step: IDEDistributionAppThinningPlistStep
2025-08-15 11:30:25 +0000 [MT] Skipping step: IDEDistributionAppThinningPlistStep because it said so
2025-08-15 11:30:25 +0000 [MT] Processing step: IDEDistributionSymbolsStep
2025-08-15 11:30:25 +0000 [MT] Writing /var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Symbols/43A2725D-0068-3CF8-8EC5-5CEC83E83CCC.symbols
2025-08-15 11:30:25 +0000 [MT] Writing /var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Symbols/302BDDCF-1C75-3243-88E0-51FC3E6B9BD6.symbols
2025-08-15 11:30:25 +0000 [MT] Writing /var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Symbols/43A2725D-0068-3CF8-8EC5-5CEC83E83CCC.symbols
2025-08-15 11:30:25 +0000 [MT] Writing /var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Symbols/302BDDCF-1C75-3243-88E0-51FC3E6B9BD6.symbols
2025-08-15 11:30:25 +0000 [MT] Processing step: IDEDistributionCopyAppleProvidedContentStep
2025-08-15 11:30:25 +0000 [MT] Processing step: IDEDistributionAppThinningStep
2025-08-15 11:30:25 +0000 [MT] Skipping step: IDEDistributionAppThinningStep because it said so
2025-08-15 11:30:25 +0000 [MT] Processing step: IDEDistributionArchThinningStep
2025-08-15 11:30:25 +0000 [MT] Item /Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app/Watch/reftest Watch App.app doesn't have the entitlement com.apple.developer.web-browser-engine.host enabled, returning ["arm64e"]
2025-08-15 11:30:25 +0000 [MT] Archs to thin for item /Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app/Watch/reftest Watch App.app are ["arm64e"]
2025-08-15 11:30:25 +0000 [MT] Running /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo '/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Root/Payload/reftest.app/Watch/reftest Watch App.app/reftest Watch App' '-verify_arch' 'arm64e'
2025-08-15 11:30:25 +0000 [MT] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo exited with 1
2025-08-15 11:30:25 +0000 [MT] Skipping architecture thinning for item "reftest Watch App" because arch "arm64e" wasn't found
2025-08-15 11:30:25 +0000 [MT] Item /Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app doesn't have the entitlement com.apple.developer.web-browser-engine.host enabled, returning ["arm64e"]
2025-08-15 11:30:25 +0000 [MT] Archs to thin for item /Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app are ["arm64e"]
2025-08-15 11:30:25 +0000 [MT] Running /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo '/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Root/Payload/reftest.app/reftest' '-verify_arch' 'arm64e'
2025-08-15 11:30:25 +0000 [MT] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo exited with 1
2025-08-15 11:30:25 +0000 [MT] Skipping architecture thinning for item "reftest" because arch "arm64e" wasn't found
2025-08-15 11:30:25 +0000 [MT] Processing step: IDEDistributionODRStep
2025-08-15 11:30:25 +0000 [MT] Processing step: IDEDistributionStripXattrsStep
2025-08-15 11:30:25 +0000 [MT] Skipping stripping extended attributes because the codesign step will strip them.
2025-08-15 11:30:25 +0000 [MT] Skipping stripping extended attributes because the codesign step will strip them.
2025-08-15 11:30:25 +0000 [MT] Processing step: IDEDistributionCodesignStep
2025-08-15 11:30:25 +0000 [MT] Entitlements for <IDEDistributionItem: 0x6000024bc960; bundleID='com.officialtech.reftest.watchkitapp', path='<DVTFilePath:0x600002f32c80:'/Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app/Watch/reftest Watch App.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x6000023fc730; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x600003529140; name='Apple Distribution: David Reynolds (3QD4HY4WVJ)', hash='4A7F2CC09A261365876BBEBEEE3556499D99AE14', serialNumber='68CC87B41A2FA8488E070E667290B320', certificateKinds='(
    "1.2.840.113635.*********",
    "1.2.840.113635.*********"
), issueDate='2025-08-08 15:45:22 +0000''>', entitlements='{
    "application-identifier" = "3QD4HY4WVJ.com.officialtech.reftest.watchkitapp";
    "beta-reports-active" = 1;
    "com.apple.developer.team-identifier" = 3QD4HY4WVJ;
    "get-task-allow" = 0;
}', teamID='3QD4HY4WVJ', identifier='com.officialtech.reftest.watchkitapp', executablePath='<DVTFilePath:0x600002f32b00:'/Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app/Watch/reftest Watch App.app/reftest Watch App'>', hardenedRuntime='0'>'>: {
    "application-identifier" = "3QD4HY4WVJ.com.officialtech.reftest.watchkitapp";
    "beta-reports-active" = 1;
    "com.apple.developer.team-identifier" = 3QD4HY4WVJ;
    "get-task-allow" = 0;
}
2025-08-15 11:30:25 +0000 [MT] Associated App Clip Identifiers Filter: Skipping because "com.apple.developer.associated-appclip-app-identifiers" is not present
2025-08-15 11:30:25 +0000 [MT] Entitlements for <IDEDistributionItem: 0x6000024bc960; bundleID='com.officialtech.reftest.watchkitapp', path='<DVTFilePath:0x600002f32c80:'/Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app/Watch/reftest Watch App.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x6000023fc730; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x600003554180; name='Apple Distribution: David Reynolds (3QD4HY4WVJ)', hash='4A7F2CC09A261365876BBEBEEE3556499D99AE14', serialNumber='68CC87B41A2FA8488E070E667290B320', certificateKinds='(
    "1.2.840.113635.*********",
    "1.2.840.113635.*********"
), issueDate='2025-08-08 15:45:22 +0000''>', entitlements='{
    "application-identifier" = "3QD4HY4WVJ.com.officialtech.reftest.watchkitapp";
    "beta-reports-active" = 1;
    "com.apple.developer.team-identifier" = 3QD4HY4WVJ;
    "get-task-allow" = 0;
}', teamID='3QD4HY4WVJ', identifier='com.officialtech.reftest.watchkitapp', executablePath='<DVTFilePath:0x600002f32b00:'/Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app/Watch/reftest Watch App.app/reftest Watch App'>', hardenedRuntime='0'>'> are: {
    "application-identifier" = "3QD4HY4WVJ.com.officialtech.reftest.watchkitapp";
    "beta-reports-active" = 1;
    "com.apple.developer.team-identifier" = 3QD4HY4WVJ;
    "get-task-allow" = 0;
}
2025-08-15 11:30:25 +0000 [MT] Writing entitlements for <IDEDistributionItem: 0x6000024bc960; bundleID='com.officialtech.reftest.watchkitapp', path='<DVTFilePath:0x600002f32c80:'/Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app/Watch/reftest Watch App.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x6000023fc730; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x600003529500; name='Apple Distribution: David Reynolds (3QD4HY4WVJ)', hash='4A7F2CC09A261365876BBEBEEE3556499D99AE14', serialNumber='68CC87B41A2FA8488E070E667290B320', certificateKinds='(
    "1.2.840.113635.*********",
    "1.2.840.113635.*********"
), issueDate='2025-08-08 15:45:22 +0000''>', entitlements='{
    "application-identifier" = "3QD4HY4WVJ.com.officialtech.reftest.watchkitapp";
    "beta-reports-active" = 1;
    "com.apple.developer.team-identifier" = 3QD4HY4WVJ;
    "get-task-allow" = 0;
}', teamID='3QD4HY4WVJ', identifier='com.officialtech.reftest.watchkitapp', executablePath='<DVTFilePath:0x600002f32b00:'/Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app/Watch/reftest Watch App.app/reftest Watch App'>', hardenedRuntime='0'>'> to: /var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/entitlements~~~1oHCAt
2025-08-15 11:30:25 +0000 [MT] invoking codesign: <NSConcreteTask: 0x6000023e28f0; launchPath='/usr/bin/codesign', arguments='(
    "-f",
    "-s",
    4A7F2CC09A261365876BBEBEEE3556499D99AE14,
    "--entitlements",
    "/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/entitlements~~~1oHCAt",
    "--preserve-metadata=identifier,flags,runtime",
    "--generate-entitlement-der",
    "--strip-disallowed-xattrs",
    "-vvv",
    "/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Root/Payload/reftest.app/Watch/reftest Watch App.app"
)'>
2025-08-15 11:30:25 +0000 [MT] codesign output: /var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Root/Payload/reftest.app/Watch/reftest Watch App.app: replacing existing signature
/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Root/Payload/reftest.app/Watch/reftest Watch App.app: signed app bundle with Mach-O universal (arm64 arm64_32) [com.officialtech.reftest.watchkitapp]
2025-08-15 11:30:25 +0000 [MT] Entitlements for <IDEDistributionItem: 0x6000024bd380; bundleID='com.officialtech.reftest', path='<DVTFilePath:0x600002410060:'/Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x6000023f92c0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x600003554000; name='Apple Distribution: David Reynolds (3QD4HY4WVJ)', hash='4A7F2CC09A261365876BBEBEEE3556499D99AE14', serialNumber='68CC87B41A2FA8488E070E667290B320', certificateKinds='(
    "1.2.840.113635.*********",
    "1.2.840.113635.*********"
), issueDate='2025-08-08 15:45:22 +0000''>', entitlements='{
    "application-identifier" = "3QD4HY4WVJ.com.officialtech.reftest";
    "beta-reports-active" = 1;
    "com.apple.developer.team-identifier" = 3QD4HY4WVJ;
    "get-task-allow" = 0;
}', teamID='3QD4HY4WVJ', identifier='com.officialtech.reftest', executablePath='<DVTFilePath:0x600002482a60:'/Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app/reftest'>', hardenedRuntime='0'>'>: {
    "application-identifier" = "3QD4HY4WVJ.com.officialtech.reftest";
    "beta-reports-active" = 1;
    "com.apple.developer.team-identifier" = 3QD4HY4WVJ;
    "get-task-allow" = 0;
}
2025-08-15 11:30:25 +0000 [MT] Associated App Clip Identifiers Filter: Skipping because "com.apple.developer.associated-appclip-app-identifiers" is not present
2025-08-15 11:30:25 +0000 [MT] Entitlements for <IDEDistributionItem: 0x6000024bd380; bundleID='com.officialtech.reftest', path='<DVTFilePath:0x600002410060:'/Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x6000023f92c0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x600003554180; name='Apple Distribution: David Reynolds (3QD4HY4WVJ)', hash='4A7F2CC09A261365876BBEBEEE3556499D99AE14', serialNumber='68CC87B41A2FA8488E070E667290B320', certificateKinds='(
    "1.2.840.113635.*********",
    "1.2.840.113635.*********"
), issueDate='2025-08-08 15:45:22 +0000''>', entitlements='{
    "application-identifier" = "3QD4HY4WVJ.com.officialtech.reftest";
    "beta-reports-active" = 1;
    "com.apple.developer.team-identifier" = 3QD4HY4WVJ;
    "get-task-allow" = 0;
}', teamID='3QD4HY4WVJ', identifier='com.officialtech.reftest', executablePath='<DVTFilePath:0x600002482a60:'/Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app/reftest'>', hardenedRuntime='0'>'> are: {
    "application-identifier" = "3QD4HY4WVJ.com.officialtech.reftest";
    "beta-reports-active" = 1;
    "com.apple.developer.team-identifier" = 3QD4HY4WVJ;
    "get-task-allow" = 0;
}
2025-08-15 11:30:25 +0000 [MT] Writing entitlements for <IDEDistributionItem: 0x6000024bd380; bundleID='com.officialtech.reftest', path='<DVTFilePath:0x600002410060:'/Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app'>', codeSigningInfo='<_DVTCodeSigningInformation_Path: 0x6000023f92c0; isSigned='1', isAdHocSigned='0', signingCertificate='<DVTSigningCertificate: 0x600003554240; name='Apple Distribution: David Reynolds (3QD4HY4WVJ)', hash='4A7F2CC09A261365876BBEBEEE3556499D99AE14', serialNumber='68CC87B41A2FA8488E070E667290B320', certificateKinds='(
    "1.2.840.113635.*********",
    "1.2.840.113635.*********"
), issueDate='2025-08-08 15:45:22 +0000''>', entitlements='{
    "application-identifier" = "3QD4HY4WVJ.com.officialtech.reftest";
    "beta-reports-active" = 1;
    "com.apple.developer.team-identifier" = 3QD4HY4WVJ;
    "get-task-allow" = 0;
}', teamID='3QD4HY4WVJ', identifier='com.officialtech.reftest', executablePath='<DVTFilePath:0x600002482a60:'/Users/<USER>/Downloads/reftest1/reftest/build/reftest.xcarchive/Products/Applications/reftest.app/reftest'>', hardenedRuntime='0'>'> to: /var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/entitlements~~~cPUbsb
2025-08-15 11:30:25 +0000 [MT] invoking codesign: <NSConcreteTask: 0x600002004c80; launchPath='/usr/bin/codesign', arguments='(
    "-f",
    "-s",
    4A7F2CC09A261365876BBEBEEE3556499D99AE14,
    "--entitlements",
    "/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/entitlements~~~cPUbsb",
    "--preserve-metadata=identifier,flags,runtime",
    "--generate-entitlement-der",
    "--strip-disallowed-xattrs",
    "-vvv",
    "/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Root/Payload/reftest.app"
)'>
2025-08-15 11:30:26 +0000 [MT] codesign output: /var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Root/Payload/reftest.app: replacing existing signature
/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Root/Payload/reftest.app: signed app bundle with Mach-O universal (arm64) [com.officialtech.reftest]
2025-08-15 11:30:26 +0000 [MT] Processing step: IDEDistributionZipODRItemStep
2025-08-15 11:30:26 +0000 [MT] Skipping step: IDEDistributionZipODRItemStep because it said so
2025-08-15 11:30:26 +0000 [MT] Skipping step: IDEDistributionZipODRItemStep because it said so
2025-08-15 11:30:26 +0000 [MT] Processing step: IDEDistributionCreateIPAStep
2025-08-15 11:30:26 +0000 [MT] Running /usr/bin/rsync '-8aPhhE' '/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Symbols' '--link-dest' '/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk' '/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Root'
2025-08-15 11:30:26 +0000  building file list ... 
2025-08-15 11:30:26 +0000   0 files...
2025-08-15 11:30:26 +0000  3 files to consider
2025-08-15 11:30:26 +0000  Symbols/
2025-08-15 11:30:26 +0000  
2025-08-15 11:30:26 +0000  sent 204 bytes  received 26 bytes  460.00 bytes/sec
total size is 899.02K  speedup is 4002.61
2025-08-15 11:30:26 +0000 [MT] /usr/bin/rsync exited with 0
2025-08-15 11:30:26 +0000 [MT] Running /usr/bin/ditto '-V' '-c' '-k' '--norsrc' '/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Root' '/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Packages/reftest.ipa'
2025-08-15 11:30:26 +0000  >>> Copying /var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Root 
2025-08-15 11:30:26 +0000  copying file ./Payload/reftest.app/_CodeSignature/CodeResources ... 
2025-08-15 11:30:26 +0000  5203 bytes for ./Payload/reftest.app/_CodeSignature/CodeResources
2025-08-15 11:30:26 +0000  copying file ./Payload/reftest.app/reftest ... 
2025-08-15 11:30:26 +0000  69264 bytes for ./Payload/reftest.app/reftest
2025-08-15 11:30:26 +0000  copying file ./Payload/reftest.app/Assets.car ... 
2025-08-15 11:30:26 +0000  25823 bytes for ./Payload/reftest.app/Assets.car
2025-08-15 11:30:26 +0000  copying file ./Payload/reftest.app/Watch/reftest Watch App.app/_CodeSignature/CodeResources ... 
2025-08-15 11:30:26 +0000  2461 bytes for ./Payload/reftest.app/Watch/reftest Watch App.app/_CodeSignature/CodeResources
2025-08-15 11:30:26 +0000  copying file ./Payload/reftest.app/Watch/reftest Watch App.app/Assets.car ... 
2025-08-15 11:30:26 +0000  74639 bytes for ./Payload/reftest.app/Watch/reftest Watch App.app/Assets.car
2025-08-15 11:30:26 +0000  copying file ./Payload/reftest.app/Watch/reftest Watch App.app/embedded.mobileprovision ... 
2025-08-15 11:30:26 +0000  12642 bytes for ./Payload/reftest.app/Watch/reftest Watch App.app/embedded.mobileprovision
2025-08-15 11:30:26 +0000  copying file ./Payload/reftest.app/Watch/reftest Watch App.app/reftest Watch App ... 
2025-08-15 11:30:26 +0000  537632 bytes for ./Payload/reftest.app/Watch/reftest Watch App.app/reftest Watch App
2025-08-15 11:30:26 +0000  copying file ./Payload/reftest.app/Watch/reftest Watch App.app/Info.plist ... 
2025-08-15 11:30:26 +0000  1180 bytes for ./Payload/reftest.app/Watch/reftest Watch App.app/Info.plist
copying file ./Payload/reftest.app/Watch/reftest Watch App.app/PkgInfo ... 
8 bytes for ./Payload/reftest.app/Watch/reftest Watch App.app/PkgInfo
2025-08-15 11:30:26 +0000  copying file ./Payload/reftest.app/MessagesApplicationStub76x76@2x~ipad.png ... 
591 bytes for ./Payload/reftest.app/MessagesApplicationStub76x76@2x~ipad.png
copying file ./Payload/reftest.app/embedded.mobileprovision ... 
2025-08-15 11:30:26 +0000  12620 bytes for ./Payload/reftest.app/embedded.mobileprovision
2025-08-15 11:30:26 +0000  copying file ./Payload/reftest.app/<EMAIL> ... 
2025-08-15 11:30:26 +0000  502 bytes for ./Payload/reftest.app/<EMAIL>
copying file ./Payload/reftest.app/Info.plist ... 
2025-08-15 11:30:26 +0000  1112 bytes for ./Payload/reftest.app/Info.plist
copying file ./Payload/reftest.app/PkgInfo ... 
8 bytes for ./Payload/reftest.app/PkgInfo
2025-08-15 11:30:26 +0000  copying file ./Symbols/302BDDCF-1C75-3243-88E0-51FC3E6B9BD6.symbols ... 
2025-08-15 11:30:26 +0000  455404 bytes for ./Symbols/302BDDCF-1C75-3243-88E0-51FC3E6B9BD6.symbols
2025-08-15 11:30:26 +0000  copying file ./Symbols/43A2725D-0068-3CF8-8EC5-5CEC83E83CCC.symbols ... 
2025-08-15 11:30:26 +0000  465196 bytes for ./Symbols/43A2725D-0068-3CF8-8EC5-5CEC83E83CCC.symbols
2025-08-15 11:30:26 +0000 [MT] /usr/bin/ditto exited with 0
2025-08-15 11:30:26 +0000 [MT] Processing step: IDEDistributionAppStoreInformationStep
2025-08-15 11:30:26 +0000 [MT] Skipping step: IDEDistributionAppStoreInformationStep because it said so
2025-08-15 11:30:26 +0000 [MT] Processing step: IDEDistributionGenerateProcessedDistributionItems
2025-08-15 11:30:26 +0000 [MT] IDEDistributionItem init <DVTFilePath:0x600002f1b780:'/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Root/Payload/reftest.app/Watch/reftest Watch App.app'>
2025-08-15 11:30:26 +0000 [MT] IDEDistributionItem init <DVTFilePath:0x6000024ccb40:'/var/folders/yg/725yzqws5wzdlz5c7d_zqg080000l2/T/XcodeDistPipeline.~~~gTZihk/Root/Payload/reftest.app'>
2025-08-15 11:30:26 +0000 [MT] Processing step: IDEDistributionCreateManifestStep
2025-08-15 11:30:26 +0000 [MT] Skipping step: IDEDistributionCreateManifestStep because it said so
