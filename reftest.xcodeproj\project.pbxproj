// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		EE749A5E2E49166100BA511B /* reftest Watch App.app in Embed Watch Content */ = {isa = PBXBuildFile; fileRef = EE749A5D2E49166100BA511B /* reftest Watch App.app */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		EE749A5F2E49166100BA511B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = EE749A512E49166100BA511B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EE749A5C2E49166100BA511B;
			remoteInfo = "reftest Watch App";
		};
		EE749A702E49166300BA511B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = EE749A512E49166100BA511B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EE749A5C2E49166100BA511B;
			remoteInfo = "reftest Watch App";
		};
		EE749A7A2E49166300BA511B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = EE749A512E49166100BA511B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EE749A5C2E49166100BA511B;
			remoteInfo = "reftest Watch App";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		EE749A862E49166300BA511B /* Embed Watch Content */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "$(CONTENTS_FOLDER_PATH)/Watch";
			dstSubfolderSpec = 16;
			files = (
				EE749A5E2E49166100BA511B /* reftest Watch App.app in Embed Watch Content */,
			);
			name = "Embed Watch Content";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		EE749A572E49166100BA511B /* reftest.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = reftest.app; sourceTree = BUILT_PRODUCTS_DIR; };
		EE749A5D2E49166100BA511B /* reftest Watch App.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "reftest Watch App.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		EE749A6F2E49166300BA511B /* reftest Watch AppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "reftest Watch AppTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		EE749A792E49166300BA511B /* reftest Watch AppUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "reftest Watch AppUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		EE749A612E49166100BA511B /* reftest Watch App */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "reftest Watch App";
			sourceTree = "<group>";
		};
		EE749A722E49166300BA511B /* reftest Watch AppTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "reftest Watch AppTests";
			sourceTree = "<group>";
		};
		EE749A7C2E49166300BA511B /* reftest Watch AppUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "reftest Watch AppUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		EE749A5A2E49166100BA511B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE749A6C2E49166300BA511B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE749A762E49166300BA511B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		EE749A502E49166100BA511B = {
			isa = PBXGroup;
			children = (
				EE749A612E49166100BA511B /* reftest Watch App */,
				EE749A722E49166300BA511B /* reftest Watch AppTests */,
				EE749A7C2E49166300BA511B /* reftest Watch AppUITests */,
				EE749A582E49166100BA511B /* Products */,
			);
			sourceTree = "<group>";
		};
		EE749A582E49166100BA511B /* Products */ = {
			isa = PBXGroup;
			children = (
				EE749A572E49166100BA511B /* reftest.app */,
				EE749A5D2E49166100BA511B /* reftest Watch App.app */,
				EE749A6F2E49166300BA511B /* reftest Watch AppTests.xctest */,
				EE749A792E49166300BA511B /* reftest Watch AppUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		EE749A562E49166100BA511B /* reftest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EE749A872E49166300BA511B /* Build configuration list for PBXNativeTarget "reftest" */;
			buildPhases = (
				EE749A552E49166100BA511B /* Resources */,
				EE749A862E49166300BA511B /* Embed Watch Content */,
			);
			buildRules = (
			);
			dependencies = (
				EE749A602E49166100BA511B /* PBXTargetDependency */,
			);
			name = reftest;
			packageProductDependencies = (
			);
			productName = reftest;
			productReference = EE749A572E49166100BA511B /* reftest.app */;
			productType = "com.apple.product-type.application.watchapp2-container";
		};
		EE749A5C2E49166100BA511B /* reftest Watch App */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EE749A832E49166300BA511B /* Build configuration list for PBXNativeTarget "reftest Watch App" */;
			buildPhases = (
				EE749A592E49166100BA511B /* Sources */,
				EE749A5A2E49166100BA511B /* Frameworks */,
				EE749A5B2E49166100BA511B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				EE749A612E49166100BA511B /* reftest Watch App */,
			);
			name = "reftest Watch App";
			packageProductDependencies = (
			);
			productName = "reftest Watch App";
			productReference = EE749A5D2E49166100BA511B /* reftest Watch App.app */;
			productType = "com.apple.product-type.application";
		};
		EE749A6E2E49166300BA511B /* reftest Watch AppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EE749A8A2E49166300BA511B /* Build configuration list for PBXNativeTarget "reftest Watch AppTests" */;
			buildPhases = (
				EE749A6B2E49166300BA511B /* Sources */,
				EE749A6C2E49166300BA511B /* Frameworks */,
				EE749A6D2E49166300BA511B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				EE749A712E49166300BA511B /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				EE749A722E49166300BA511B /* reftest Watch AppTests */,
			);
			name = "reftest Watch AppTests";
			packageProductDependencies = (
			);
			productName = "reftest Watch AppTests";
			productReference = EE749A6F2E49166300BA511B /* reftest Watch AppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		EE749A782E49166300BA511B /* reftest Watch AppUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EE749A8D2E49166300BA511B /* Build configuration list for PBXNativeTarget "reftest Watch AppUITests" */;
			buildPhases = (
				EE749A752E49166300BA511B /* Sources */,
				EE749A762E49166300BA511B /* Frameworks */,
				EE749A772E49166300BA511B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				EE749A7B2E49166300BA511B /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				EE749A7C2E49166300BA511B /* reftest Watch AppUITests */,
			);
			name = "reftest Watch AppUITests";
			packageProductDependencies = (
			);
			productName = "reftest Watch AppUITests";
			productReference = EE749A792E49166300BA511B /* reftest Watch AppUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		EE749A512E49166100BA511B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					EE749A562E49166100BA511B = {
						CreatedOnToolsVersion = 16.2;
					};
					EE749A5C2E49166100BA511B = {
						CreatedOnToolsVersion = 16.2;
					};
					EE749A6E2E49166300BA511B = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = EE749A5C2E49166100BA511B;
					};
					EE749A782E49166300BA511B = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = EE749A5C2E49166100BA511B;
					};
				};
			};
			buildConfigurationList = EE749A542E49166100BA511B /* Build configuration list for PBXProject "reftest" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = EE749A502E49166100BA511B;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = EE749A582E49166100BA511B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				EE749A562E49166100BA511B /* reftest */,
				EE749A5C2E49166100BA511B /* reftest Watch App */,
				EE749A6E2E49166300BA511B /* reftest Watch AppTests */,
				EE749A782E49166300BA511B /* reftest Watch AppUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		EE749A552E49166100BA511B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE749A5B2E49166100BA511B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE749A6D2E49166300BA511B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE749A772E49166300BA511B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		EE749A592E49166100BA511B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE749A6B2E49166300BA511B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EE749A752E49166300BA511B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		EE749A602E49166100BA511B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EE749A5C2E49166100BA511B /* reftest Watch App */;
			targetProxy = EE749A5F2E49166100BA511B /* PBXContainerItemProxy */;
		};
		EE749A712E49166300BA511B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EE749A5C2E49166100BA511B /* reftest Watch App */;
			targetProxy = EE749A702E49166300BA511B /* PBXContainerItemProxy */;
		};
		EE749A7B2E49166300BA511B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EE749A5C2E49166100BA511B /* reftest Watch App */;
			targetProxy = EE749A7A2E49166300BA511B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		EE749A812E49166300BA511B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		EE749A822E49166300BA511B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		EE749A842E49166300BA511B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 16;
				DEVELOPMENT_ASSET_PATHS = "\"reftest Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=watchos*]" = 3QD4HY4WVJ;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = reftest;
				INFOPLIST_KEY_NSHealthShareUsageDescription = "This app uses HealthKit to keep workouts running in the background";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "This app uses HealthKit to keep workouts running in the background";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKWatchOnly = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.officialtech.reftest.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=watchos*]" = ref3;
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 11.2;
			};
			name = Debug;
		};
		EE749A852E49166300BA511B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 16;
				DEVELOPMENT_ASSET_PATHS = "\"reftest Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=watchos*]" = 3QD4HY4WVJ;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleDisplayName = reftest;
				INFOPLIST_KEY_NSHealthShareUsageDescription = "This app uses HealthKit to keep workouts running in the background";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "This app uses HealthKit to keep workouts running in the background";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKWatchOnly = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.officialtech.reftest.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=watchos*]" = ref3;
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 11.2;
			};
			name = Release;
		};
		EE749A882E49166300BA511B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 16;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 3QD4HY4WVJ;
				INFOPLIST_KEY_CFBundleDisplayName = reftest;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.officialtech.reftest;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "caompa 1";
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		EE749A892E49166300BA511B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 16;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 3QD4HY4WVJ;
				INFOPLIST_KEY_CFBundleDisplayName = reftest;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.officialtech.reftest;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "caompa 1";
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		EE749A8B2E49166300BA511B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.officialtech.reftest-Watch-AppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/reftest Watch App.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/reftest Watch App";
				WATCHOS_DEPLOYMENT_TARGET = 11.2;
			};
			name = Debug;
		};
		EE749A8C2E49166300BA511B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.officialtech.reftest-Watch-AppTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/reftest Watch App.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/reftest Watch App";
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 11.2;
			};
			name = Release;
		};
		EE749A8E2E49166300BA511B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.officialtech.reftest-Watch-AppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_TARGET_NAME = "reftest Watch App";
				WATCHOS_DEPLOYMENT_TARGET = 11.2;
			};
			name = Debug;
		};
		EE749A8F2E49166300BA511B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.officialtech.reftest-Watch-AppUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				TEST_TARGET_NAME = "reftest Watch App";
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 11.2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		EE749A542E49166100BA511B /* Build configuration list for PBXProject "reftest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EE749A812E49166300BA511B /* Debug */,
				EE749A822E49166300BA511B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EE749A832E49166300BA511B /* Build configuration list for PBXNativeTarget "reftest Watch App" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EE749A842E49166300BA511B /* Debug */,
				EE749A852E49166300BA511B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EE749A872E49166300BA511B /* Build configuration list for PBXNativeTarget "reftest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EE749A882E49166300BA511B /* Debug */,
				EE749A892E49166300BA511B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EE749A8A2E49166300BA511B /* Build configuration list for PBXNativeTarget "reftest Watch AppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EE749A8B2E49166300BA511B /* Debug */,
				EE749A8C2E49166300BA511B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EE749A8D2E49166300BA511B /* Build configuration list for PBXNativeTarget "reftest Watch AppUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EE749A8E2E49166300BA511B /* Debug */,
				EE749A8F2E49166300BA511B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = EE749A512E49166100BA511B /* Project object */;
}
