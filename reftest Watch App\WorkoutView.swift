import SwiftUI
import WatchKit
import Combine
import HealthKit

// MARK: - WorkoutSessionManager (FINAL, CORRECTED VERSION FOR PROPER SHUTDOWN)
final class WorkoutSessionManager: NSObject, ObservableObject, HKWorkoutSessionDelegate, HKLiveWorkoutBuilderDelegate {
   private let healthStore = HKHealthStore()
   private var session: HKWorkoutSession?
   private var builder: HKLiveWorkoutBuilder?
   @Published private(set) var isRunning = false

   func startWorkout(completion: @escaping (Bool) -> Void) {
       let shareTypes: Set<HKSampleType> = [HKObjectType.workoutType()]
       let readTypes: Set<HKObjectType> = []

       healthStore.requestAuthorization(toShare: shareTypes, read: readTypes) { [weak self] success, error in
           guard let self = self, success else {
               DispatchQueue.main.async { completion(false) }
               return
           }

           do {
               let config = HKWorkoutConfiguration()
               config.activityType = .traditionalStrengthTraining
               config.locationType = .indoor

               let session = try HKWorkoutSession(healthStore: self.healthStore, configuration: config)
               let builder = session.associatedWorkoutBuilder()
               builder.dataSource = HKLiveWorkoutDataSource(healthStore: self.healthStore, workoutConfiguration: config)

               session.delegate = self
               builder.delegate = self
               self.session = session
               self.builder = builder

               let startDate = Date()
               session.startActivity(with: startDate)
               builder.beginCollection(withStart: startDate) { successBegin, errorBegin in
                   DispatchQueue.main.async {
                       self.isRunning = successBegin
                       completion(successBegin)
                   }
               }
           } catch {
               DispatchQueue.main.async { completion(false) }
           }
       }
   }

   // *** THIS IS THE FIX FOR THE APP QUITTING ***
   func stopWorkout() {
       guard let session = session, let builder = builder else { return }

       // Apple's recommended three-step teardown process:
       // 1. End the builder's collection
       builder.endCollection(withEnd: Date()) { [weak self] (success, error) in
           guard let self = self else { return }

           // 2. Finish the workout (saves to HealthKit)
           builder.finishWorkout { (workout, error) in
               DispatchQueue.main.async {
                   // 3. End the session
                   session.end()

                   // Clear references
                   self.session = nil
                   self.builder = nil
                   self.isRunning = false

                   if let error = error {
                       print("Error finishing workout: \(error.localizedDescription)")
                   } else {
                       print("Workout saved successfully to HealthKit")
                   }
               }
           }
       }
   }

   func workoutSession(_ workoutSession: HKWorkoutSession, didChangeTo toState: HKWorkoutSessionState, from fromState: HKWorkoutSessionState, date: Date) {
       DispatchQueue.main.async { self.isRunning = (toState == .running) }
   }

   func workoutSession(_ workoutSession: HKWorkoutSession, didFailWithError error: Error) {
       DispatchQueue.main.async { self.stopWorkout() }
   }

   func workoutBuilder(_ workoutBuilder: HKLiveWorkoutBuilder, didCollectDataOf collectedTypes: Set<HKSampleType>) {
       // Heart rate data collection helps keep the app active
       if collectedTypes.contains(HKQuantityType.quantityType(forIdentifier: .heartRate)!) {
           print("Heart rate data collected - app staying active")
       }
   }

   func workoutBuilderDidCollectEvent(_ workoutBuilder: HKLiveWorkoutBuilder) { }
}

struct WorkoutView: View {
   @Environment(\.dismiss) private var dismiss
   @AppStorage("jogSeconds") private var jogSeconds = 15
   @AppStorage("walkSeconds") private var walkSeconds = 25
   @AppStorage("intervals") private var totalIntervals = 15

   // Persistent state using @AppStorage to survive app kills
   @AppStorage("currentInterval") private var currentInterval = 0
   @AppStorage("isJogPhase") private var isJogPhase = true
   @AppStorage("isActive") private var isActive = false
   @AppStorage("phaseEndTimestamp") private var phaseEndTimestamp: Double = 0
   @AppStorage("workoutStartTimestamp") private var workoutStartTimestamp: Double = 0

   // UI state that doesn't need persistence
   @State private var timeRemaining: Double = 15.0
   @State private var showingOptions = false
   @State private var progressAnimation: Double = 0.0
   @State private var showingCompletion = false
   @State private var hasvibrated3s = false
   @State private var hasvibrated2s = false
   @State private var hasvibrated1s = false
   @State private var hasVibratedHalfTime = false
   @State private var forceProgressUpdate = false // Force progress bar update
   @State private var lastDisplayedSeconds = 0 // Track displayed seconds for vibration detection

   // Computed properties for precise timing
   private var workoutStartDate: Date? {
       workoutStartTimestamp > 0 ? Date(timeIntervalSince1970: workoutStartTimestamp) : nil
   }

   private var phaseEndDate: Date? {
       phaseEndTimestamp > 0 ? Date(timeIntervalSince1970: phaseEndTimestamp) : nil
   }

   // Calculate precise phase end time based on workout start and intervals
   private var calculatedPhaseEndDate: Date? {
       guard let startDate = workoutStartDate else { return nil }

       var totalElapsed: TimeInterval = 0
       for i in 0..<currentInterval {
           totalElapsed += Double(jogSeconds) + Double(walkSeconds)
       }

       if isJogPhase {
           totalElapsed += Double(jogSeconds)
       } else {
           totalElapsed += Double(jogSeconds) + Double(walkSeconds)
       }

       return startDate.addingTimeInterval(totalElapsed)
   }
   @StateObject private var workoutManager = WorkoutSessionManager()
   @Environment(\.scenePhase) private var scenePhase

   private var currentPhaseTitle: String { isJogPhase ? "JOG" : "WALK" }
   private var currentPhaseDuration: Double { isJogPhase ? Double(jogSeconds) : Double(walkSeconds) }
   private var progress: Double {
       let totalTime = currentPhaseDuration
       guard totalTime > 0 else { return 0 }
       let elapsed = totalTime - timeRemaining
       return min(max(Double(elapsed) / Double(totalTime), 0), 1)
   }
   private var progressBarColor: Color {
       if timeRemaining <= 3.5 && timeRemaining > 0 { return .red } else { return .green }
   }

   var body: some View {
       GeometryReader { geometry in
           let screenWidth = geometry.size.width
           let screenHeight = geometry.size.height
           let baseWidth: CGFloat = 176
           let baseHeight: CGFloat = 215
           let widthScale = screenWidth / baseWidth
           let heightScale = screenHeight / baseHeight
           let scale = min(widthScale, heightScale)
           let titleFontSize = max(20 * scale, 16)
           let timerFontSize = max(32 * scale, 24)
           let intervalFontSize = max(16 * scale, 12)
           let buttonFontSize = max(16 * scale, 12)
           let helperFontSize = max(11 * scale, 9)
           let mainSpacing = max(10 * scale, 6)
           let progressHeight = max(12 * scale, 10)
           let buttonHeight = max(44 * scale, 36)
           
           ZStack {
               Color.black.ignoresSafeArea()
               
               if !isActive && !showingCompletion && !showingOptions {
                   VStack {
                       HStack {
                           Button(action: { dismiss() }) {
                               Image(systemName: "xmark")
                                   .font(.system(size: max(18 * scale, 14), weight: .medium))
                                   .foregroundColor(.white)
                                   .frame(width: max(30 * scale, 24), height: max(30 * scale, 24))
                                   .background(Color.gray.opacity(0.6))
                                   .clipShape(Circle())
                           }
                           .buttonStyle(PlainButtonStyle())
                           Spacer()
                       }
                       .padding(.leading, max(16 * scale, 12))
                       .padding(.top, max(8 * scale, 4))
                       .offset(y: -27)
                       Spacer()
                   }
                   .zIndex(1)
               }

               if showingCompletion {
                   VStack(spacing: mainSpacing) {
                       Spacer()
                       Text("Test Completed Successfully")
                           .font(.system(size: buttonFontSize, weight: .medium))
                           .foregroundColor(.white)
                           .multilineTextAlignment(.center)
                           .padding(.horizontal, 16)
                       Button(action: { dismiss() }) {
                           Text("Done")
                               .font(.system(size: buttonFontSize, weight: .medium))
                               .foregroundColor(.white)
                               .frame(maxWidth: .infinity)
                               .frame(height: buttonHeight)
                               .background(Color.gray)
                               .cornerRadius(8)
                       }
                       .buttonStyle(PlainButtonStyle())
                       .padding(.horizontal, 16)
                       Spacer()
                   }
               } else if showingOptions {
                   VStack(spacing: mainSpacing) {
                       Spacer()
                       VStack(spacing: 8) {
                           Button(action: { 
                               // FIX: Reset animation when returning from options
                               print("Return pressed - resetting progress animation")
                               resetProgressAnimation()
                               showingOptions = false 
                           }) {
                               Text("Return")
                                   .font(.system(size: buttonFontSize, weight: .medium))
                                   .foregroundColor(.white)
                                   .frame(maxWidth: .infinity)
                                   .frame(height: buttonHeight * 0.85)
                                   .background(Color.gray)
                                   .cornerRadius(8)
                           }
                           .buttonStyle(PlainButtonStyle())
                           Button(action: {
                               restartWorkout()
                               showingOptions = false
                           }) {
                               Text("Restart")
                                   .font(.system(size: buttonFontSize, weight: .medium))
                                   .foregroundColor(.black)
                                   .frame(maxWidth: .infinity)
                                   .frame(height: buttonHeight * 0.85)
                                   .background(Color.orange)
                                   .cornerRadius(8)
                           }
                           .buttonStyle(PlainButtonStyle())
                           Button(action: {
                               stopWorkout()
                               dismiss()
                           }) {
                               Text("Abandon")
                                   .font(.system(size: buttonFontSize, weight: .medium))
                                   .foregroundColor(.white)
                                   .frame(maxWidth: .infinity)
                                   .frame(height: buttonHeight * 0.85)
                                   .background(Color.red)
                                   .cornerRadius(8)
                           }
                           .buttonStyle(PlainButtonStyle())
                       }
                       .padding(.horizontal, 16)
                       Spacer()
                   }
               } else {
                   VStack {
                       HStack {
                           Text("RefTest")
                               .font(.system(size: 16, weight: .semibold))
                               .foregroundColor(.white)
                           Spacer()
                       }
                       .padding(.horizontal)
                       .padding(.top, -48.8)

                       VStack(spacing: mainSpacing) {
                           Text(currentPhaseTitle)
                               .font(.system(size: titleFontSize, weight: .bold))
                               .foregroundColor(.white)
                               .offset(y: -10)
                               .scaleEffect(1.95)
                           
                           // Fix: Use TimelineView instead of Text(timerInterval:) to prevent slowdown
                           TimelineView(.periodic(from: Date(), by: 0.1)) { context in
                               Text(timeString(from: timeRemaining))
                                   .font(.system(size: timerFontSize, weight: .bold))
                                   .monospacedDigit()
                           }
                           .foregroundColor(.white)
                           
                           VStack(spacing: mainSpacing) {
                               ZStack(alignment: .leading) {
                                   Rectangle().fill(Color.gray.opacity(0.3)).frame(height: progressHeight).cornerRadius(progressHeight / 2)
                                   Rectangle().fill(progressBarColor).frame(width: screenWidth * 0.8 * CGFloat(progressAnimation), height: progressHeight).cornerRadius(progressHeight / 2)
                               }
                               .frame(width: screenWidth * 0.8)

                               Text("\(currentInterval)/\(totalIntervals)")
                                   .font(.system(size: intervalFontSize, weight: .semibold))
                                   .foregroundColor(.white)
                                   .padding(.horizontal, max(16 * scale, 12))
                                   .padding(.vertical, max(8 * scale, 6))
                                   .background(Color.gray.opacity(0.4))
                                   .cornerRadius(max(16 * scale, 12))
                                   .offset(y: 4.3)
                           }

                           Spacer(minLength: 5)

                           VStack {
                               if !isActive {
                                   Button(action: {
                                       print("Begin button pressed!")
                                       print("Current phase duration: \(currentPhaseDuration)")
                                       print("Jog duration: \(jogSeconds), Walk duration: \(walkSeconds)")
                                       print("Total intervals: \(totalIntervals)")

                                       // IMMEDIATE state change - no lingering
                                       withAnimation(.none) {
                                           resetVibrationFlags()
                                           currentInterval = 0
                                           isJogPhase = true
                                           timeRemaining = currentPhaseDuration
                                           progressAnimation = 0.0
                                       }
                                       startWorkout()
                                   }) {
                                       Text("Begin")
                                           .font(.system(size: buttonFontSize, weight: .semibold))
                                           .foregroundColor(.black)
                                           .frame(maxWidth: .infinity)
                                           .frame(height: buttonHeight)
                                           .background(Color.green)
                                           .cornerRadius(10)
                                           .offset(y: -1.7)
                                   }
                                   .buttonStyle(PlainButtonStyle())
                                   .padding(.horizontal, max(20 * scale, 16))
                               } else {
                                   Text("Long press for options")
                                       .font(.system(size: helperFontSize))
                                       .foregroundColor(.gray)
                                       .frame(height: buttonHeight)
                               }
                           }
                       }
                   }
                   .padding(.vertical, max(8 * scale, 4))
                   .frame(maxWidth: .infinity, maxHeight: .infinity)
                   .onLongPressGesture(minimumDuration: 0.4) {
                       if isActive { showingOptions = true }
                   }
               }
           }
       }
       .toolbar(.hidden, for: .navigationBar)
       .onAppear {
           timeRemaining = currentPhaseDuration
           progressAnimation = 0.0
           resetVibrationFlags()
       }
       .onDisappear { stopWorkout() }
       .onReceive(Timer.publish(every: 0.5, on: .main, in: .common).autoconnect()) { _ in
           // Only update when workout is active and throttle updates
           guard isActive else { return }
           updateTimer()
       }
       .onReceive(NotificationCenter.default.publisher(for: .NSCalendarDayChanged)) { _ in
           // Handle day changes if needed
       }
   }

   // NEW FUNCTION: Reset progress animation to match actual time remaining
   private func resetProgressAnimation() {
       guard isActive else { return }
       
       let totalTime = currentPhaseDuration
       let elapsed = totalTime - timeRemaining
       let currentProgress = min(max(elapsed / totalTime, 0), 1)
       
       print("Resetting progress animation:")
       print("- Total time: \(totalTime)")
       print("- Time remaining: \(timeRemaining)")
       print("- Elapsed: \(elapsed)")
       print("- Current progress: \(currentProgress)")
       
       // Force update to trigger view refresh
       forceProgressUpdate.toggle()
       
       // Stop current animation immediately
       withAnimation(.none) {
           progressAnimation = currentProgress
       }
       
       // Small delay to ensure the immediate update takes effect
       DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
           // Start new animation for remaining time
           let remainingTime = max(self.timeRemaining, 0)
           if remainingTime > 0 {
               withAnimation(.linear(duration: remainingTime)) {
                   self.progressAnimation = 1.0
               }
           }
           print("New animation started for remaining time: \(remainingTime)")
       }
   }

   private func updateTimer() {
       guard isActive else { return }

       // Use calculated precise end date for accuracy
       guard let endDate = calculatedPhaseEndDate else { return }

       let remaining = endDate.timeIntervalSinceNow

       if remaining <= 0 {
           timeRemaining = 0
           handlePhaseCompletion()
       } else {
           timeRemaining = remaining
           // Only handle vibrations every 0.5 seconds to reduce CPU usage
           handleVibrations(timeRemaining: remaining)
       }
   }

   private func handleVibrations(timeRemaining: Double) {
       // NEW APPROACH: Vibrate when displayed seconds change from 5→4, 4→3, 3→2
       let currentDisplayedSeconds = Int(max(0, round(timeRemaining)))
       
       // Only check for changes if we have a previous value
       if lastDisplayedSeconds != currentDisplayedSeconds {
           print("Second changed from \(lastDisplayedSeconds) to \(currentDisplayedSeconds)")
           
           // Vibrate when transitioning TO these specific seconds
           if currentDisplayedSeconds == 4 && !hasvibrated3s {
               WKInterfaceDevice.current().play(.notification)
               hasvibrated3s = true
               print("Vibrated on transition TO 4 seconds")
           }
           else if currentDisplayedSeconds == 3 && !hasvibrated2s {
               WKInterfaceDevice.current().play(.notification)
               hasvibrated2s = true
               print("Vibrated on transition TO 3 seconds")
           }
           else if currentDisplayedSeconds == 2 && !hasvibrated1s {
               WKInterfaceDevice.current().play(.notification)
               hasvibrated1s = true
               print("Vibrated on transition TO 2 seconds")
           }
           
           lastDisplayedSeconds = currentDisplayedSeconds
       }
       
       // Keep half-time vibration logic as is (works well)
       let halfTime = currentPhaseDuration / 2.0
       if currentPhaseDuration > 15.0 && timeRemaining <= halfTime && timeRemaining > (halfTime - 1.0) && !hasVibratedHalfTime {
           WKInterfaceDevice.current().play(.notification)
           hasVibratedHalfTime = true
           print("Vibrated at half-time mark (timeRemaining: \(timeRemaining), halfTime: \(halfTime))")
       }
   }

   private func startWorkout() {
       print("Starting workout...")
       guard !isActive else {
           print("Workout already active, skipping start")
           return
       }

       // Set precise workout start time for accurate timing
       let startTime = Date()
       workoutStartTimestamp = startTime.timeIntervalSince1970

       print("Setting up workout - duration: \(currentPhaseDuration), phase: \(isJogPhase ? "jog" : "walk")")
       isActive = true
       timeRemaining = currentPhaseDuration

       // Calculate precise phase end time based on workout start
       if let preciseEndDate = calculatedPhaseEndDate {
           phaseEndTimestamp = preciseEndDate.timeIntervalSince1970
           print("Phase end date set to: \(preciseEndDate)")
       }

       // NO lingering animations - immediate transition with .none
       progressAnimation = 0.0
       withAnimation(.none) {
           progressAnimation = 0.0
       }
       // Then start the actual progress animation
       withAnimation(.linear(duration: currentPhaseDuration)) {
           progressAnimation = 1.0
       }

       resetVibrationFlags()
       workoutManager.startWorkout { success in
           print("HealthKit workout started: \(success)")
       }

       print("Workout started successfully")
   }

   private func restartWorkout() {
       stopWorkout()
       currentInterval = 0
       isJogPhase = true
       timeRemaining = currentPhaseDuration
       progressAnimation = 0.0
       resetVibrationFlags()
       startWorkout()
   }

   private func stopWorkout() {
       isActive = false
       phaseEndTimestamp = 0
       workoutManager.stopWorkout()
   }

   private func handlePhaseCompletion() {
       print("Phase completion - current interval: \(currentInterval), total: \(totalIntervals)")

       // REMOVED: Phase completion vibration (no longer vibrates at 0s)
       // WKInterfaceDevice.current().play(.notification)

       // Ensure we're actually at 0 before completing
       timeRemaining = 0

       // Update phase and interval
       if isJogPhase {
           print("Switching from jog to walk")
           isJogPhase = false
       } else {
           print("Switching from walk to jog, incrementing interval")
           isJogPhase = true
           currentInterval += 1
       }

       // Check if workout is complete
       if currentInterval >= totalIntervals {
           print("Workout completed! Intervals: \(currentInterval)/\(totalIntervals)")
           stopWorkout()
           showingCompletion = true
           return
       }

       // Start next phase with precise timing based on calculated end date
       let nextPhaseDuration = currentPhaseDuration
       print("Starting next phase - duration: \(nextPhaseDuration), phase: \(isJogPhase ? "jog" : "walk")")
       timeRemaining = nextPhaseDuration

       // Use calculated precise end date for accuracy
       if let preciseEndDate = calculatedPhaseEndDate {
           phaseEndTimestamp = preciseEndDate.timeIntervalSince1970
           print("Next phase precise end date: \(preciseEndDate)")
       }

       // IMMEDIATE transition - NO lingering animations
       withAnimation(.none) {
           progressAnimation = 0.0
       }
       resetVibrationFlags()

       // Start new phase progress animation immediately after reset
       withAnimation(.linear(duration: nextPhaseDuration)) {
           progressAnimation = 1.0
       }

       print("Next phase started successfully")
   }

   private func resetVibrationFlags() {
       hasvibrated3s = false
       hasvibrated2s = false
       hasvibrated1s = false
       hasVibratedHalfTime = false
       lastDisplayedSeconds = Int(max(0, round(timeRemaining))) // Reset displayed seconds tracker
   }

   private func timeString(from seconds: Double) -> String {
       let wholeSeconds = Int(max(0, round(seconds)))
       let minutes = wholeSeconds / 60
       let remainingSeconds = wholeSeconds % 60
       return String(format: "%d:%02d", minutes, remainingSeconds)
   }
}

#Preview {
   WorkoutView()
}