import SwiftUI
import WatchKit
import Combine
import HealthKit

// MARK: - WorkoutSessionManager (FINAL, CORRECTED VERSION FOR PROPER SHUTDOWN)
final class WorkoutSessionManager: NSObject, ObservableObject, HKWorkoutSessionDelegate, HKLiveWorkoutBuilderDelegate {
   private let healthStore = HKHealthStore()
   private var session: HKWorkoutSession?
   private var builder: HKLiveWorkoutBuilder?
   @Published private(set) var isRunning = false

   func startWorkout(completion: @escaping (Bool) -> Void) {
       let shareTypes: Set<HKSampleType> = [HKObjectType.workoutType()]
       let readTypes: Set<HKObjectType> = []

       healthStore.requestAuthorization(toShare: shareTypes, read: readTypes) { [weak self] success, error in
           guard let self = self, success else {
               DispatchQueue.main.async { completion(false) }
               return
           }

           do {
               let config = HKWorkoutConfiguration()
               config.activityType = .traditionalStrengthTraining
               config.locationType = .indoor

               let session = try HKWorkoutSession(healthStore: self.healthStore, configuration: config)
               let builder = session.associatedWorkoutBuilder()
               builder.dataSource = HKLiveWorkoutDataSource(healthStore: self.healthStore, workoutConfiguration: config)

               session.delegate = self
               builder.delegate = self
               self.session = session
               self.builder = builder

               let startDate = Date()
               session.startActivity(with: startDate)
               builder.beginCollection(withStart: startDate) { successBegin, errorBegin in
                   DispatchQueue.main.async {
                       self.isRunning = successBegin
                       completion(successBegin)
                   }
               }
           } catch {
               DispatchQueue.main.async { completion(false) }
           }
       }
   }

   // *** THIS IS THE FIX FOR THE APP QUITTING ***
   func stopWorkout() {
       guard let session = session, let builder = builder else { return }

       // Apple's recommended three-step teardown process:
       // 1. End the builder's collection
       builder.endCollection(withEnd: Date()) { [weak self] (success, error) in
           guard let self = self else { return }

           // 2. Finish the workout (saves to HealthKit)
           builder.finishWorkout { (workout, error) in
               DispatchQueue.main.async {
                   // 3. End the session
                   session.end()

                   // Clear references
                   self.session = nil
                   self.builder = nil
                   self.isRunning = false

                   if let error = error {
                       print("Error finishing workout: \(error.localizedDescription)")
                   } else {
                       print("Workout saved successfully to HealthKit")
                   }
               }
           }
       }
   }

   func workoutSession(_ workoutSession: HKWorkoutSession, didChangeTo toState: HKWorkoutSessionState, from fromState: HKWorkoutSessionState, date: Date) {
       DispatchQueue.main.async { self.isRunning = (toState == .running) }
   }

   func workoutSession(_ workoutSession: HKWorkoutSession, didFailWithError error: Error) {
       DispatchQueue.main.async { self.stopWorkout() }
   }

   func workoutBuilder(_ workoutBuilder: HKLiveWorkoutBuilder, didCollectDataOf collectedTypes: Set<HKSampleType>) {
       // Heart rate data collection helps keep the app active
       if collectedTypes.contains(HKQuantityType.quantityType(forIdentifier: .heartRate)!) {
           print("Heart rate data collected - app staying active")
       }
   }

   func workoutBuilderDidCollectEvent(_ workoutBuilder: HKLiveWorkoutBuilder) { }
}

struct WorkoutView: View {
   @Environment(\.dismiss) private var dismiss
   @AppStorage("jogSeconds") private var jogSeconds = 15
   @AppStorage("walkSeconds") private var walkSeconds = 25
   @AppStorage("intervals") private var totalIntervals = 15

   // Persistent state using @AppStorage to survive app kills
   @AppStorage("currentInterval") private var currentInterval = 0
   @AppStorage("isJogPhase") private var isJogPhase = true
   @AppStorage("isActive") private var isActive = false
   @AppStorage("phaseEndTimestamp") private var phaseEndTimestamp: Double = 0
   @AppStorage("workoutStartTimestamp") private var workoutStartTimestamp: Double = 0

   // UI state that doesn't need persistence
   @State private var timeRemaining: Double = 15.0
   @State private var showingOptions = false

   @State private var showingCompletion = false
   @State private var hasVibrated3s = false
   @State private var hasVibrated2s = false
   @State private var hasVibrated1s = false
   @State private var hasVibratedHalfTime = false

   @State private var lastDisplayedSeconds = 0 // Track displayed seconds for vibration detection

   // Computed properties for precise timing
   private var workoutStartDate: Date? {
       workoutStartTimestamp > 0 ? Date(timeIntervalSince1970: workoutStartTimestamp) : nil
   }

   private var phaseEndDate: Date? {
       phaseEndTimestamp > 0 ? Date(timeIntervalSince1970: phaseEndTimestamp) : nil
   }

   // Calculate precise phase end time based on workout start and intervals
   private var calculatedPhaseEndDate: Date? {
       guard let startDate = workoutStartDate else { return nil }

       var totalElapsed: TimeInterval = 0
       for i in 0..<currentInterval {
           totalElapsed += Double(jogSeconds) + Double(walkSeconds)
       }

       if isJogPhase {
           totalElapsed += Double(jogSeconds)
       } else {
           totalElapsed += Double(jogSeconds) + Double(walkSeconds)
       }

       return startDate.addingTimeInterval(totalElapsed)
   }
   @StateObject private var workoutManager = WorkoutSessionManager()
   @Environment(\.scenePhase) private var scenePhase

   private var currentPhaseTitle: String { isJogPhase ? "JOG" : "WALK" }
   private var currentPhaseDuration: Double { isJogPhase ? Double(jogSeconds) : Double(walkSeconds) }


   var body: some View {
       GeometryReader { geometry in
           let screenWidth = geometry.size.width
           let screenHeight = geometry.size.height
           let baseWidth: CGFloat = 176
           let baseHeight: CGFloat = 215
           let widthScale = screenWidth / baseWidth
           let heightScale = screenHeight / baseHeight
           let scale = min(widthScale, heightScale)
           let titleFontSize = max(20 * scale, 16)
           let timerFontSize = max(32 * scale, 24)
           let intervalFontSize = max(16 * scale, 12)
           let buttonFontSize = max(16 * scale, 12)
           let helperFontSize = max(11 * scale, 9)
           let mainSpacing = max(10 * scale, 6)

           let buttonHeight = max(44 * scale, 36)
           
           ZStack {
               Color.black.ignoresSafeArea()
               
               VStack {
                   HStack {
                       Button(action: { dismiss() }) {
                           Image(systemName: "xmark")
                               .font(.system(size: max(18 * scale, 14), weight: .medium))
                               .foregroundColor(.white)
                               .frame(width: max(30 * scale, 24), height: max(30 * scale, 24))
                               .background(Color.gray.opacity(0.6))
                               .clipShape(Circle())
                       }
                       .buttonStyle(PlainButtonStyle())
                       .opacity(!isActive && !showingCompletion && !showingOptions ? 1 : 0)
                       .animation(.none, value: isActive)
                       .disabled(isActive || showingCompletion || showingOptions)
                       Spacer()
                   }
                   .padding(.leading, max(16 * scale, 12))
                   .padding(.top, max(8 * scale, 4))
                   .offset(y: -27)
                   Spacer()
               }
               .zIndex(1)

               if showingCompletion {
                   VStack(spacing: mainSpacing) {
                       Spacer()
                       Text("Test Completed Successfully")
                           .font(.system(size: buttonFontSize, weight: .medium))
                           .foregroundColor(.white)
                           .multilineTextAlignment(.center)
                           .padding(.horizontal, 16)
                       Button(action: { dismiss() }) {
                           Text("Done")
                               .font(.system(size: buttonFontSize, weight: .medium))
                               .foregroundColor(.white)
                               .frame(maxWidth: .infinity)
                               .frame(height: buttonHeight)
                               .background(Color.gray)
                               .cornerRadius(8)
                       }
                       .buttonStyle(PlainButtonStyle())
                       .padding(.horizontal, 16)
                       Spacer()
                   }
               } else if showingOptions {
                   VStack(spacing: mainSpacing) {
                       Spacer()
                       VStack(spacing: 8) {
                           Button(action: {
                               showingOptions = false
                           }) {
                               Text("Return")
                                   .font(.system(size: buttonFontSize, weight: .medium))
                                   .foregroundColor(.white)
                                   .frame(maxWidth: .infinity)
                                   .frame(height: buttonHeight * 0.85)
                                   .background(Color.gray)
                                   .cornerRadius(8)
                           }
                           .buttonStyle(PlainButtonStyle())
                           Button(action: {
                               restartWorkout()
                               showingOptions = false
                           }) {
                               Text("Restart")
                                   .font(.system(size: buttonFontSize, weight: .medium))
                                   .foregroundColor(.black)
                                   .frame(maxWidth: .infinity)
                                   .frame(height: buttonHeight * 0.85)
                                   .background(Color.orange)
                                   .cornerRadius(8)
                           }
                           .buttonStyle(PlainButtonStyle())
                           Button(action: {
                               stopWorkout()
                               dismiss()
                           }) {
                               Text("Abandon")
                                   .font(.system(size: buttonFontSize, weight: .medium))
                                   .foregroundColor(.white)
                                   .frame(maxWidth: .infinity)
                                   .frame(height: buttonHeight * 0.85)
                                   .background(Color.red)
                                   .cornerRadius(8)
                           }
                           .buttonStyle(PlainButtonStyle())
                       }
                       .padding(.horizontal, 16)
                       Spacer()
                   }
               } else {
                   VStack {
                       HStack {
                           Text("RefTest")
                               .font(.system(size: 16, weight: .semibold))
                               .foregroundColor(.white)
                           Spacer()
                       }
                       .padding(.horizontal)
                       .padding(.top, -48.8)

                       VStack(spacing: mainSpacing) {
                           Text(currentPhaseTitle)
                               .font(.system(size: titleFontSize, weight: .bold))
                               .foregroundColor(.white)
                               .offset(y: -10)
                               .scaleEffect(1.55)
                           
                           // Fix: Use TimelineView instead of Text(timerInterval:) to prevent slowdown
                           TimelineView(.periodic(from: Date(), by: 0.1)) { context in
                               Text(timeString(from: timeRemaining))
                                   .font(.system(size: timerFontSize * 1.5, weight: .bold)) // Made 1.5x bigger
                                   .monospacedDigit()
                           }
                           .foregroundColor(.white)
                           
                           VStack(spacing: mainSpacing) {
                               Text("\(currentInterval)/\(totalIntervals)")
                                   .font(.system(size: intervalFontSize, weight: .semibold))
                                   .foregroundColor(.white)
                                   .padding(.horizontal, max(16 * scale, 12))
                                   .padding(.vertical, max(8 * scale, 6))
                                   .background(Color.gray.opacity(0.4))
                                   .cornerRadius(max(16 * scale, 12))
                                   .offset(y: 15) // Moved down more
                           }

                           Spacer(minLength: 5)

                           VStack {
                               Button(action: {
                                   print("Begin button pressed!")
                                   print("Current phase duration: \(currentPhaseDuration)")
                                   print("Jog duration: \(jogSeconds), Walk duration: \(walkSeconds)")
                                   print("Total intervals: \(totalIntervals)")

                                   // IMMEDIATE state change - no lingering
                                   withAnimation(.none) {
                                       resetVibrationFlags()
                                       currentInterval = 0
                                       isJogPhase = true
                                       timeRemaining = currentPhaseDuration
                                   }
                                   startWorkout()
                               }) {
                                   Text("Begin")
                                       .font(.system(size: buttonFontSize, weight: .semibold))
                                       .foregroundColor(.black)
                                       .frame(maxWidth: .infinity)
                                       .frame(height: buttonHeight)
                                       .background(Color.green)
                                       .cornerRadius(10)
                                       .offset(y: 10) // Moved down
                               }
                               .buttonStyle(PlainButtonStyle())
                               .padding(.horizontal, max(20 * scale, 16))
                               .opacity(!isActive ? 1 : 0)
                               .animation(.none, value: isActive)
                               .disabled(isActive)

                               Text("Long press for options")
                                   .font(.system(size: helperFontSize))
                                   .foregroundColor(.gray)
                                   .frame(height: buttonHeight)
                                   .offset(y: 10) // Moved down
                           }
                       }
                   }
                   .padding(.vertical, max(8 * scale, 4))
                   .frame(maxWidth: .infinity, maxHeight: .infinity)
                   .onLongPressGesture(minimumDuration: 0.4) {
                       if isActive { showingOptions = true }
                   }
               }
           }
       }
       .toolbar(.hidden, for: .navigationBar)
       .onAppear {
           timeRemaining = currentPhaseDuration
           resetVibrationFlags()
       }
       .onDisappear { stopWorkout() }
       .onReceive(Timer.publish(every: 0.5, on: .main, in: .common).autoconnect()) { _ in
           // Only update when workout is active and throttle updates
           guard isActive else { return }
           updateTimer()
       }
       .onReceive(NotificationCenter.default.publisher(for: .NSCalendarDayChanged)) { _ in
           // Handle day changes if needed
       }
   }



   private func updateTimer() {
       guard isActive else { return }

       // Use calculated precise end date for accuracy
       guard let endDate = calculatedPhaseEndDate else { return }

       let remaining = endDate.timeIntervalSinceNow

       if remaining <= 0 {
           timeRemaining = 0
           handlePhaseCompletion()
       } else {
           timeRemaining = remaining
           // Only handle vibrations every 0.5 seconds to reduce CPU usage
           handleVibrations(timeRemaining: remaining)
       }
   }

   private func handleVibrations(timeRemaining: Double) {
       // NEW APPROACH: Vibrate when displayed seconds change from 5→4, 4→3, 3→2
       let currentDisplayedSeconds = Int(max(0, round(timeRemaining)))
       
       // Only check for changes if we have a previous value
       if lastDisplayedSeconds != currentDisplayedSeconds {
           print("Second changed from \(lastDisplayedSeconds) to \(currentDisplayedSeconds)")
           
           // Vibrate when transitioning TO these specific seconds
           if currentDisplayedSeconds == 3 && !hasVibrated3s {
               WKInterfaceDevice.current().play(.notification)
               hasVibrated3s = true
               print("Vibrated on transition TO 3 seconds")
           }
           else if currentDisplayedSeconds == 2 && !hasVibrated2s {
               WKInterfaceDevice.current().play(.notification)
               hasVibrated2s = true
               print("Vibrated on transition TO 2 seconds")
           }
           else if currentDisplayedSeconds == 1 && !hasVibrated1s {
               WKInterfaceDevice.current().play(.notification)
               hasVibrated1s = true
               print("Vibrated on transition TO 1 seconds")
           }
           
           lastDisplayedSeconds = currentDisplayedSeconds
       }
       
       // Keep half-time vibration logic as is (works well)
       let halfTime = currentPhaseDuration / 2.0
       if currentPhaseDuration > 15.0 && timeRemaining <= halfTime && timeRemaining > (halfTime - 1.0) && !hasVibratedHalfTime {
           WKInterfaceDevice.current().play(.notification)
           hasVibratedHalfTime = true
           print("Vibrated at half-time mark (timeRemaining: \(timeRemaining), halfTime: \(halfTime))")
       }
   }

   private func startWorkout() {
       print("Starting workout...")
       guard !isActive else {
           print("Workout already active, skipping start")
           return
       }

       // Set precise workout start time for accurate timing
       let startTime = Date()
       workoutStartTimestamp = startTime.timeIntervalSince1970

       print("Setting up workout - duration: \(currentPhaseDuration), phase: \(isJogPhase ? "jog" : "walk")")
       isActive = true
       timeRemaining = currentPhaseDuration

       // Calculate precise phase end time based on workout start
       if let preciseEndDate = calculatedPhaseEndDate {
           phaseEndTimestamp = preciseEndDate.timeIntervalSince1970
           print("Phase end date set to: \(preciseEndDate)")
       }

       // Progress bar removed - no animations needed

       resetVibrationFlags()
       workoutManager.startWorkout { success in
           print("HealthKit workout started: \(success)")
       }

       print("Workout started successfully")
   }

   private func restartWorkout() {
       stopWorkout()
       currentInterval = 0
       isJogPhase = true
       timeRemaining = currentPhaseDuration
       resetVibrationFlags()
       startWorkout()
   }

   private func stopWorkout() {
       isActive = false
       phaseEndTimestamp = 0
       workoutManager.stopWorkout()
   }

   private func handlePhaseCompletion() {
       print("Phase completion - current interval: \(currentInterval), total: \(totalIntervals)")

       // REMOVED: Phase completion vibration (no longer vibrates at 0s)
       // WKInterfaceDevice.current().play(.notification)

       // Ensure we're actually at 0 before completing
       timeRemaining = 0

       // Update phase and interval
       if isJogPhase {
           print("Switching from jog to walk")
           isJogPhase = false
       } else {
           print("Switching from walk to jog, incrementing interval")
           isJogPhase = true
           currentInterval += 1
       }

       // Check if workout is complete
       if currentInterval >= totalIntervals {
           print("Workout completed! Intervals: \(currentInterval)/\(totalIntervals)")
           stopWorkout()
           showingCompletion = true
           return
       }

       // Start next phase with precise timing based on calculated end date
       let nextPhaseDuration = currentPhaseDuration
       print("Starting next phase - duration: \(nextPhaseDuration), phase: \(isJogPhase ? "jog" : "walk")")
       timeRemaining = nextPhaseDuration

       // Use calculated precise end date for accuracy
       if let preciseEndDate = calculatedPhaseEndDate {
           phaseEndTimestamp = preciseEndDate.timeIntervalSince1970
           print("Next phase precise end date: \(preciseEndDate)")
       }

       // Reset vibration flags for new phase
       resetVibrationFlags()

       print("Next phase started successfully")
   }

   private func resetVibrationFlags() {
       hasVibrated3s = false
       hasVibrated2s = false
       hasVibrated1s = false
       hasVibratedHalfTime = false
       lastDisplayedSeconds = Int(max(0, round(timeRemaining))) // Reset displayed seconds tracker
   }

   private func timeString(from seconds: Double) -> String {
       let wholeSeconds = Int(max(0, round(seconds)))
       let minutes = wholeSeconds / 60
       let remainingSeconds = wholeSeconds % 60
       return String(format: "%d:%02d", minutes, remainingSeconds)
   }
}

#Preview {
   WorkoutView()
}